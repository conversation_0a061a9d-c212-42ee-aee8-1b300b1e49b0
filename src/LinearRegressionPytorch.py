"""
Linear Regression with Shape Safety - Python/PyTorch Implementation
Dataset: Boston Housing (13 features -> 1 target)
Goal: Demonstrate runtime shape errors vs compile-time safety
"""

import torch
import numpy as np
import time
from sklearn.datasets import load_boston
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

class LinearRegressionPyTorch:
    def __init__(self, input_dim=13, learning_rate=0.01):
        """
        Initialize linear regression model
        Args:
            input_dim: Number of input features (13 for Boston Housing)
            learning_rate: Learning rate for gradient descent
        """
        self.input_dim = input_dim
        self.learning_rate = learning_rate
        
        # Initialize weights and bias - POTENTIAL SHAPE ERROR SOURCE
        self.weights = torch.randn(input_dim, 1, requires_grad=True)
        self.bias = torch.randn(1, requires_grad=True)
        
        # Track metrics
        self.training_losses = []
        self.runtime_errors = []
        
    def forward(self, X):
        """
        Forward pass - POTENTIAL RUNTIME SHAPE ERROR
        Args:
            X: Input tensor of shape [batch_size, input_dim]
        Returns:
            predictions: Tensor of shape [batch_size, 1]
        """
        try:
            # This can cause runtime errors if shapes don't match
            predictions = X @ self.weights + self.bias
            return predictions
        except RuntimeError as e:
            error_msg = f"Runtime shape error in forward pass: {str(e)}"
            self.runtime_errors.append(error_msg)
            print(f"ERROR: {error_msg}")
            raise
    
    def compute_loss(self, predictions, targets):
        """
        Compute MSE loss - POTENTIAL SHAPE MISMATCH
        """
        try:
            loss = torch.mean((predictions - targets) ** 2)
            return loss
        except RuntimeError as e:
            error_msg = f"Runtime shape error in loss computation: {str(e)}"
            self.runtime_errors.append(error_msg)
            print(f"ERROR: {error_msg}")
            raise
    
    def train_step(self, X, y):
        """
        Single training step
        """
        # Zero gradients
        if self.weights.grad is not None:
            self.weights.grad.zero_()
        if self.bias.grad is not None:
            self.bias.grad.zero_()
        
        # Forward pass
        predictions = self.forward(X)
        
        # Compute loss
        loss = self.compute_loss(predictions, y)
        
        # Backward pass
        loss.backward()
        
        # Update parameters
        with torch.no_grad():
            self.weights -= self.learning_rate * self.weights.grad
            self.bias -= self.learning_rate * self.bias.grad
        
        return loss.item()
    
    def train(self, X_train, y_train, epochs=1000, verbose=True):
        """
        Training loop with error tracking
        """
        start_time = time.time()
        
        for epoch in range(epochs):
            loss = self.train_step(X_train, y_train)
            self.training_losses.append(loss)
            
            if verbose and epoch % 100 == 0:
                print(f"Epoch {epoch}, Loss: {loss:.6f}")
        
        training_time = time.time() - start_time
        return training_time
    
    def predict(self, X):
        """
        Make predictions
        """
        with torch.no_grad():
            return self.forward(X)

def demonstrate_shape_errors():
    """
    Deliberately introduce shape errors to demonstrate runtime failures
    """
    print("=== Demonstrating Potential Shape Errors ===")
    
    # Create model expecting 13 features
    model = LinearRegressionPyTorch(input_dim=13)
    
    # Test 1: Correct shapes (should work)
    print("\nTest 1: Correct shapes")
    X_correct = torch.randn(100, 13)  # 100 samples, 13 features
    y_correct = torch.randn(100, 1)   # 100 targets
    try:
        pred = model.forward(X_correct)
        print(f"✓ Success: Input {X_correct.shape} -> Output {pred.shape}")
    except Exception as e:
        print(f"✗ Error: {e}")
    
    # Test 2: Wrong input dimension (should fail)
    print("\nTest 2: Wrong input dimension")
    X_wrong = torch.randn(100, 10)    # Wrong: 10 features instead of 13
    try:
        pred = model.forward(X_wrong)
        print(f"✓ Unexpected success: Input {X_wrong.shape} -> Output {pred.shape}")
    except Exception as e:
        print(f"✗ Runtime Error (as expected): {e}")
    
    # Test 3: Wrong target dimension for loss
    print("\nTest 3: Wrong target dimension for loss")
    X_correct = torch.randn(100, 13)
    y_wrong = torch.randn(100, 5)     # Wrong: 5 outputs instead of 1
    try:
        pred = model.forward(X_correct)
        loss = model.compute_loss(pred, y_wrong)
        print(f"✓ Unexpected success: Loss computed")
    except Exception as e:
        print(f"✗ Runtime Error (as expected): {e}")

def load_and_preprocess_data():
    """
    Load and preprocess Boston Housing dataset
    """
    print("=== Loading Boston Housing Dataset ===")
    
    # Load dataset
    boston = load_boston()
    X, y = boston.data, boston.target
    
    print(f"Dataset shape: X={X.shape}, y={y.shape}")
    print(f"Features: {boston.feature_names}")
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42
    )
    
    # Standardize features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # Convert to PyTorch tensors
    X_train_tensor = torch.FloatTensor(X_train_scaled)
    X_test_tensor = torch.FloatTensor(X_test_scaled)
    y_train_tensor = torch.FloatTensor(y_train).unsqueeze(1)  # Add dimension
    y_test_tensor = torch.FloatTensor(y_test).unsqueeze(1)
    
    return X_train_tensor, X_test_tensor, y_train_tensor, y_test_tensor

def main():
    """
    Main experimental function
    """
    print("Linear Regression Shape Safety Experiment - Python/PyTorch")
    print("=" * 60)
    
    # Demonstrate shape errors first
    demonstrate_shape_errors()
    
    print("\n" + "=" * 60)
    print("=== Actual Training on Boston Housing ===")
    
    # Load data
    X_train, X_test, y_train, y_test = load_and_preprocess_data()
    
    # Create and train model
    model = LinearRegressionPyTorch(input_dim=13, learning_rate=0.01)
    
    print(f"\nTraining on data: X_train={X_train.shape}, y_train={y_train.shape}")
    
    # Track development time
    start_time = time.time()
    training_time = model.train(X_train, y_train, epochs=1000, verbose=True)
    total_dev_time = time.time() - start_time
    
    # Make predictions
    train_pred = model.predict(X_train)
    test_pred = model.predict(X_test)
    
    # Calculate metrics
    train_mse = mean_squared_error(y_train.numpy(), train_pred.numpy())
    test_mse = mean_squared_error(y_test.numpy(), test_pred.numpy())
    train_r2 = r2_score(y_train.numpy(), train_pred.numpy())
    test_r2 = r2_score(y_test.numpy(), test_pred.numpy())
    
    # Print results
    print(f"\n=== Results ===")
    print(f"Training time: {training_time:.4f} seconds")
    print(f"Total development time: {total_dev_time:.4f} seconds")
    print(f"Runtime errors encountered: {len(model.runtime_errors)}")
    if model.runtime_errors:
        for error in model.runtime_errors:
            print(f"  - {error}")
    
    print(f"\nPerformance Metrics:")
    print(f"Train MSE: {train_mse:.4f}")
    print(f"Test MSE: {test_mse:.4f}")
    print(f"Train R²: {train_r2:.4f}")
    print(f"Test R²: {test_r2:.4f}")
    
    # Code metrics
    import inspect
    code_lines = len(inspect.getsource(LinearRegressionPyTorch).split('\n'))
    print(f"\nCode Metrics:")
    print(f"Lines of code (class): {code_lines}")
    
    return {
        'training_time': training_time,
        'total_dev_time': total_dev_time,
        'runtime_errors': len(model.runtime_errors),
        'test_mse': test_mse,
        'test_r2': test_r2,
        'code_lines': code_lines,
        'errors_list': model.runtime_errors
    }

if __name__ == "__main__":
    results = main()
    print(f"\nExperiment completed. Results: {results}")