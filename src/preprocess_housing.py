#!/usr/bin/env python3
"""
California Housing Dataset Preprocessing Script

This script preprocesses the California housing dataset for training linear regression models
using both Idris2 with Spidr and Python with PyTorch.

Directory structure created:
- data/
  - train_features.csv
  - train_targets.csv  
  - test_features.csv
  - test_targets.csv
  - plots/
    - correlation_matrix.png
    - feature_distributions.png
    - target_distribution.png
    - scatter_plots.png
    - feature_summary.png
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.datasets import fetch_california_housing
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import os
import warnings
warnings.filterwarnings('ignore')

def create_directories():
    """Create necessary directory structure"""
    directories = ['data', 'data/plots']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    print("📁 Created directory structure")

def load_california_housing_data():
    """Load and return California housing dataset"""
    print("🏠 Loading California housing dataset...")
    housing = fetch_california_housing()
    
    # Create DataFrame for easier manipulation
    feature_names = housing.feature_names
    features_df = pd.DataFrame(housing.data, columns=feature_names)
    targets_df = pd.DataFrame(housing.target, columns=['MedHouseVal'])
    
    print(f"   Dataset shape: {features_df.shape}")
    print(f"   Features: {list(feature_names)}")
    print(f"   Target: Median house value in hundreds of thousands of dollars")
    
    return features_df, targets_df, feature_names

def explore_data(features_df, targets_df):
    """Perform exploratory data analysis and generate visualizations"""
    print("🔍 Performing exploratory data analysis...")
    
    # Basic statistics
    print("\n📊 Dataset Summary:")
    print(f"   Number of samples: {len(features_df)}")
    print(f"   Number of features: {len(features_df.columns)}")
    print(f"   Missing values: {features_df.isnull().sum().sum()}")
    print(f"   Target range: ${targets_df['MedHouseVal'].min():.2f}k - ${targets_df['MedHouseVal'].max():.2f}k")
    print(f"   Target mean: ${targets_df['MedHouseVal'].mean():.2f}k")
    
    # Set up plotting style
    plt.style.use('default')
    sns.set_palette("husl")
    
    # 1. Correlation matrix
    print("   📈 Creating correlation matrix...")
    plt.figure(figsize=(12, 10))
    correlation_data = pd.concat([features_df, targets_df], axis=1)
    correlation_matrix = correlation_data.corr()
    
    mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
    sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='coolwarm', 
                center=0, square=True, fmt='.2f', cbar_kws={"shrink": .8})
    plt.title('California Housing Dataset - Correlation Matrix', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('data/plots/correlation_matrix.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. Feature distributions
    print("   📊 Creating feature distribution plots...")
    fig, axes = plt.subplots(2, 4, figsize=(20, 10))
    axes = axes.ravel()
    
    for i, column in enumerate(features_df.columns):
        axes[i].hist(features_df[column], bins=50, alpha=0.7, edgecolor='black', linewidth=0.5)
        axes[i].set_title(f'{column}', fontweight='bold')
        axes[i].set_xlabel(column)
        axes[i].set_ylabel('Frequency')
        axes[i].grid(True, alpha=0.3)
    
    plt.suptitle('California Housing Dataset - Feature Distributions', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('data/plots/feature_distributions.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. Target distribution
    print("   🎯 Creating target distribution plot...")
    plt.figure(figsize=(12, 6))
    
    plt.subplot(1, 2, 1)
    plt.hist(targets_df['MedHouseVal'], bins=50, alpha=0.7, edgecolor='black', linewidth=0.5)
    plt.title('Target Distribution (Histogram)', fontweight='bold')
    plt.xlabel('Median House Value (100k $)')
    plt.ylabel('Frequency')
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 2, 2)
    plt.boxplot(targets_df['MedHouseVal'])
    plt.title('Target Distribution (Box Plot)', fontweight='bold')
    plt.ylabel('Median House Value (100k $)')
    plt.grid(True, alpha=0.3)
    
    plt.suptitle('California Housing Dataset - Target Distribution', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('data/plots/target_distribution.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 4. Scatter plots of features vs target
    print("   🔗 Creating feature vs target scatter plots...")
    fig, axes = plt.subplots(2, 4, figsize=(20, 10))
    axes = axes.ravel()
    
    for i, column in enumerate(features_df.columns):
        axes[i].scatter(features_df[column], targets_df['MedHouseVal'], alpha=0.5, s=1)
        axes[i].set_xlabel(column)
        axes[i].set_ylabel('Median House Value')
        axes[i].set_title(f'{column} vs Target', fontweight='bold')
        axes[i].grid(True, alpha=0.3)
    
    plt.suptitle('California Housing Dataset - Features vs Target', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('data/plots/scatter_plots.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 5. Summary statistics visualization
    print("   📋 Creating feature summary statistics...")
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 12))
    
    # Statistical summary table
    summary_stats = features_df.describe().round(3)
    ax1.axis('tight')
    ax1.axis('off')
    table = ax1.table(cellText=summary_stats.values,
                     rowLabels=summary_stats.index,
                     colLabels=summary_stats.columns,
                     cellLoc='center',
                     loc='center',
                     bbox=[0, 0, 1, 1])
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1.2, 2)
    ax1.set_title('Feature Summary Statistics', fontweight='bold', fontsize=14, pad=20)
    
    # Feature ranges visualization
    feature_ranges = features_df.max() - features_df.min()
    ax2.bar(range(len(feature_ranges)), feature_ranges.values)
    ax2.set_xlabel('Features')
    ax2.set_ylabel('Range (Max - Min)')
    ax2.set_title('Feature Ranges', fontweight='bold')
    ax2.set_xticks(range(len(feature_ranges)))
    ax2.set_xticklabels(feature_ranges.index, rotation=45)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('data/plots/feature_summary.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return correlation_matrix

def preprocess_data(features_df, targets_df):
    """Preprocess the data for both Idris2/Spidr and PyTorch"""
    print("⚙️  Preprocessing data...")
    
    # Split data into train and test sets
    X_train, X_test, y_train, y_test = train_test_split(
        features_df, targets_df, test_size=0.2, random_state=42, stratify=None
    )
    
    print(f"   Training set: {X_train.shape[0]} samples")
    print(f"   Test set: {X_test.shape[0]} samples")
    
    # Standardize features (important for linear regression)
    scaler = StandardScaler()
    X_train_scaled = pd.DataFrame(
        scaler.fit_transform(X_train), 
        columns=X_train.columns,
        index=X_train.index
    )
    X_test_scaled = pd.DataFrame(
        scaler.transform(X_test), 
        columns=X_test.columns,
        index=X_test.index
    )
    
    print("   ✅ Features standardized (mean=0, std=1)")
    
    # Display scaling information
    print(f"   Original feature ranges:")
    for col in features_df.columns:
        orig_range = features_df[col].max() - features_df[col].min()
        print(f"      {col}: {orig_range:.2f}")
    
    print(f"   Scaled feature ranges (should be ~6-8 for normal distribution):")
    for col in X_train_scaled.columns:
        scaled_range = X_train_scaled[col].max() - X_train_scaled[col].min()
        print(f"      {col}: {scaled_range:.2f}")
    
    return X_train_scaled, X_test_scaled, y_train, y_test, scaler

def save_data_for_idris_spidr(X_train, X_test, y_train, y_test):
    """Save data in CSV format suitable for Idris2/Spidr"""
    print("💾 Saving data for Idris2/Spidr...")
    
    # Save as CSV files (Idris2 can read CSV easily)
    X_train.to_csv('data/train_features.csv', index=False, header=True)
    X_test.to_csv('data/test_features.csv', index=False, header=True)
    y_train.to_csv('data/train_targets.csv', index=False, header=True)
    y_test.to_csv('data/test_targets.csv', index=False, header=True)
    
    # Also save dimensions for Idris2 type safety
    train_dims = {
        'num_train_samples': len(X_train),
        'num_test_samples': len(X_test),
        'num_features': len(X_train.columns),
        'feature_names': list(X_train.columns)
    }
    
    with open('data/dimensions.txt', 'w') as f:
        f.write(f"-- Dataset dimensions for Idris2 type safety\n")
        f.write(f"-- Generated automatically by preprocessing script\n\n")
        f.write(f"NUM_TRAIN_SAMPLES : Nat\n")
        f.write(f"NUM_TRAIN_SAMPLES = {train_dims['num_train_samples']}\n\n")
        f.write(f"NUM_TEST_SAMPLES : Nat\n")
        f.write(f"NUM_TEST_SAMPLES = {train_dims['num_test_samples']}\n\n")
        f.write(f"NUM_FEATURES : Nat\n")
        f.write(f"NUM_FEATURES = {train_dims['num_features']}\n\n")
        f.write(f"-- Feature names: {', '.join(train_dims['feature_names'])}\n")
    
    print(f"   ✅ Saved train_features.csv ({X_train.shape})")
    print(f"   ✅ Saved test_features.csv ({X_test.shape})")
    print(f"   ✅ Saved train_targets.csv ({y_train.shape})")
    print(f"   ✅ Saved test_targets.csv ({y_test.shape})")
    print(f"   ✅ Saved dimensions.txt for Idris2 type safety")

def save_data_for_pytorch(X_train, X_test, y_train, y_test):
    """Save data in formats suitable for PyTorch (can also read CSV)"""
    print("🔥 Data already suitable for PyTorch (CSV format)")
    
    # Save as numpy arrays for direct PyTorch loading (optional)
    np.save('data/train_features.npy', X_train.values)
    np.save('data/test_features.npy', X_test.values)
    np.save('data/train_targets.npy', y_train.values)
    np.save('data/test_targets.npy', y_test.values)
    
    print(f"   ✅ Also saved .npy files for direct PyTorch loading")

def generate_readme():
    """Generate README file with dataset information"""
    readme_content = """# California Housing Dataset - Preprocessed

This directory contains the preprocessed California housing dataset for training linear regression models using both Idris2 with Spidr and Python with PyTorch.

## Dataset Information

- **Source**: sklearn.datasets.fetch_california_housing()
- **Features**: 8 numerical features
- **Target**: Median house value (in hundreds of thousands of dollars)
- **Samples**: ~20,640 total (split 80/20 train/test)
- **Preprocessing**: Features standardized (mean=0, std=1)

## Features Description

1. **MedInc**: Median income in block group
2. **HouseAge**: Median house age in block group
3. **AveRooms**: Average number of rooms per household
4. **AveBedrms**: Average number of bedrooms per household
5. **Population**: Block group population
6. **AveOccup**: Average number of household members
7. **Latitude**: Latitude coordinate
8. **Longitude**: Longitude coordinate

## Files

### Data Files
- `train_features.csv`: Training features (standardized)
- `train_targets.csv`: Training targets
- `test_features.csv`: Test features (standardized)
- `test_targets.csv`: Test targets
- `dimensions.txt`: Dataset dimensions for Idris2 type safety
- `*.npy`: NumPy format files for direct PyTorch loading

### Visualizations (plots/ directory)
- `correlation_matrix.png`: Feature correlation heatmap
- `feature_distributions.png`: Histograms of all features
- `target_distribution.png`: Target variable distribution
- `scatter_plots.png`: Features vs target scatter plots
- `feature_summary.png`: Statistical summary visualization

## Usage

### For Idris2 with Spidr
The CSV files can be loaded using Idris2's CSV parsing capabilities. The `dimensions.txt` file provides compile-time constants for type safety.

### For PyTorch
Load the CSV files using pandas or use the .npy files for direct NumPy array loading.

## Data Quality Notes

- No missing values in the dataset
- All features are numerical (no categorical encoding needed)
- Features have been standardized to prevent scale-related issues
- Train/test split uses random_state=42 for reproducibility
"""
    
    with open('data/README.md', 'w') as f:
        f.write(readme_content)
    
    print("📝 Generated README.md with dataset documentation")

def main():
    """Main preprocessing pipeline"""
    print("🚀 Starting California Housing Dataset Preprocessing")
    print("="*60)
    
    # Create directory structure
    create_directories()
    
    # Load data
    features_df, targets_df, feature_names = load_california_housing_data()
    
    # Explore and visualize data
    correlation_matrix = explore_data(features_df, targets_df)
    
    # Preprocess data
    X_train, X_test, y_train, y_test, scaler = preprocess_data(features_df, targets_df)
    
    # Save data for both frameworks
    save_data_for_idris_spidr(X_train, X_test, y_train, y_test)
    save_data_for_pytorch(X_train, X_test, y_train, y_test)
    
    # Generate documentation
    generate_readme()
    
    print("\n" + "="*60)
    print("✅ Preprocessing completed successfully!")
    print("\nGenerated files:")
    print("📁 data/")
    print("   📄 train_features.csv, train_targets.csv")
    print("   📄 test_features.csv, test_targets.csv")
    print("   📄 dimensions.txt (for Idris2)")
    print("   📄 *.npy files (for PyTorch)")
    print("   📄 README.md")
    print("   📁 plots/")
    print("      🖼️  correlation_matrix.png")
    print("      🖼️  feature_distributions.png")
    print("      🖼️  target_distribution.png")
    print("      🖼️  scatter_plots.png")
    print("      🖼️  feature_summary.png")
    
    print(f"\n🎯 Dataset ready for linear regression experiments!")
    print(f"   Training samples: {len(X_train)}")
    print(f"   Test samples: {len(X_test)}")
    print(f"   Features: {len(X_train.columns)}")
    print(f"   Strong feature correlations with target:")
    
    # Show most correlated features
    target_corr = correlation_matrix['MedHouseVal'].abs().sort_values(ascending=False)
    for feature, corr in target_corr.head(4).items():
        if feature != 'MedHouseVal':
            print(f"      {feature}: {corr:.3f}")

if __name__ == "__main__":
    main()