# California Housing Dataset - Preprocessed

This directory contains the preprocessed California housing dataset for training linear regression models using both Idris2 with Spidr and Python with PyTorch.

## Dataset Information

- **Source**: sklearn.datasets.fetch_california_housing()
- **Features**: 8 numerical features
- **Target**: Median house value (in hundreds of thousands of dollars)
- **Samples**: ~20,640 total (split 80/20 train/test)
- **Preprocessing**: Features standardized (mean=0, std=1)

## Features Description

1. **MedInc**: Median income in block group
2. **HouseAge**: Median house age in block group
3. **AveRooms**: Average number of rooms per household
4. **AveBedrms**: Average number of bedrooms per household
5. **Population**: Block group population
6. **AveOccup**: Average number of household members
7. **Latitude**: Latitude coordinate
8. **Longitude**: Longitude coordinate

## Files

### Data Files
- `train_features.csv`: Training features (standardized)
- `train_targets.csv`: Training targets
- `test_features.csv`: Test features (standardized)
- `test_targets.csv`: Test targets
- `dimensions.txt`: Dataset dimensions for Idris2 type safety
- `*.npy`: NumPy format files for direct PyTorch loading

### Visualizations (plots/ directory)
- `correlation_matrix.png`: Feature correlation heatmap
- `feature_distributions.png`: Histograms of all features
- `target_distribution.png`: Target variable distribution
- `scatter_plots.png`: Features vs target scatter plots
- `feature_summary.png`: Statistical summary visualization

## Usage

### For Idris2 with Spidr
The CSV files can be loaded using Idris2's CSV parsing capabilities. The `dimensions.txt` file provides compile-time constants for type safety.

### For PyTorch
Load the CSV files using pandas or use the .npy files for direct NumPy array loading.

## Data Quality Notes

- No missing values in the dataset
- All features are numerical (no categorical encoding needed)
- Features have been standardized to prevent scale-related issues
- Train/test split uses random_state=42 for reproducibility
